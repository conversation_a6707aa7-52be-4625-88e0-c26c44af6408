#include "manager.h"

Manager::Manager(string name, string pwd)
{
	this->m_Name = name;
	this->m_Pwd = pwd;
}
Manager::~Manager()
{
}
void Manager::operMenu()
{
	cout.clear();
	cout << "============================" << endl;
	cout << "1.add person" << endl;
	cout << "2.show person" << endl;
	cout << "3.show room" << endl;
	cout << "4.clean file" << endl;
	cout << "0.exit" << endl;
	cout << "============================" << endl;
	cout << "please input：";
}
void Manager::addPerson()
{
}
void Manager::showPerson()
{
}
void Manager::showRoom()
{
}
void Manager::cleanFile()
{
}