#include "Student.h"

Student::Student(int id, string name, string pwd)
{
	this->m_Id = id;
	this->m_Name = name;
	this->m_Pwd = pwd;
}

Student::~Student()
{
}

void Student::operMenu()
{
	cout.clear();
	cout << "============================" << endl;
	cout << "1.apply order" << endl;
	cout << "2.show my order" << endl;
	cout << "3.show all order" << endl;
	cout << "4.cancel order" << endl;
	cout << "0.exit" << endl;
	cout << "============================" << endl;
	cout << "please input";
}

void Student::applyOrder()
{
}

void Student::showMyOrder()
{
}

void Student::showAllOrder()
{
}

void Student::cancelOrder()
{
}