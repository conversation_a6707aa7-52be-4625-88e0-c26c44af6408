#include <iostream>
#include <unordered_map>
#include <map>
#include <fstream>
#include <vector>
#include "enum.h"
#include "globalFile.h"
#include "Identity.h"
#include "student.h"
#include "teacher.h"
#include "manager.h"

using namespace std;

struct FileUserInfo
{
	int id;
	string name;
	string pwd;
	FileUserInfo(int i, string n, string p) : id(i), name(n), pwd(p) {};
};

bool judgmentPwd(int id, const string &name, const string &pwd, const string &fileName, bool isManager = false)
{
	ifstream ifs(fileName, ios::in);
	if (!ifs.is_open())
	{
		cout << "open file failed" << endl;
		return false;
	}
	vector<FileUserInfo> users;
	string fName, fPwd;
	// 一次性读取到内存
	if (isManager)
	{
		while (ifs >> fName >> fPwd)
		{
			users.emplace_back(0, fName, fPwd);
		}
	}
	else
	{
		int fId;
		while (ifs >> fId >> fName >> fPwd)
		{
			users.emplace_back(fId, fName, fPwd);
		}
	}
	ifs.close();

	// 判断
	auto it = find_if(users.begin(), users.end(), [&](const FileUserInfo &user)
					  { return user.id == id && user.name == name && user.pwd == pwd; });
	if (it != users.end())
	{
		cout.clear();
		cout << "login success" << endl;
		return true;
	}
	else
	{
		cout << "login failed" << endl;
		return false;
	}
}

void login(unordered_map<int, UserTypeInfo>::iterator it)
{
	Identity *person = nullptr; // 使用基类指针
	int id;
	string name;
	string pwd;

	UserType userType = static_cast<UserType>(it->first);
	if (userType == USER_STUDENT || userType == USER_TEACHER)
	{
		cout << "please input ID:";
		cin >> id;
		cout << "please input name:";
		cin >> name;
		cout << "please input password:";
		cin >> pwd;
		if (judgmentPwd(id, name, pwd, it->second.fileName))
		{
			// 三元运算要求结果指针要一样，所以要用static_cast显式转换
			person = userType == USER_STUDENT ? static_cast<Identity *>(new Student(id, name, pwd)) : static_cast<Identity *>(new Teacher(id, name, pwd));
		}
	}
	else if (userType == USER_MANAGER)
	{
		cout << "please input name:";
		cin >> name;
		cout << "please input password:";
		cin >> pwd;
		if (judgmentPwd(0, name, pwd, it->second.fileName, true))
		{
			person = new Manager(name, pwd);
		}
	}

	if (person != nullptr) {
		person->operMenu(); // 调用操作菜单函数
	}
}

// 方案1：使用 unordered_map 存储完整的用户信息 - O(1) 查找效率
unordered_map<int, UserTypeInfo> userTypeMap = {
	{USER_STUDENT, {"Student", STUDENT_FILE}},
	{USER_TEACHER, {"Teacher", TEACHER_FILE}},
	{USER_MANAGER, {"Manager", ADMIN_FILE}}};

int main()
{
	int select = 0;
	const int WIDTH = 28;
	while (true)
	{
		system("cls");
		cout << "============================" << endl;
		// 遍历显示所有用户类型选项
		// 旧写法
		/*for (const auto& pair : userTypeMap) {
			cout << pair.first << "." << pair.second.label << endl;
		}*/
		// 新写法 C++20 结构化绑定
		for (const auto &[key, value] : userTypeMap)
		{
			const string s = to_string(key) + "." + value.label;
			cout.width(WIDTH); // 只作用到下一个cout，这样写b不行：cout << a << b
			cout << s << endl;
		}
		cout.width(WIDTH);
		cout << "0.exit" << endl;
		cout << "============================" << endl;
		cout << "please input：";
		cin >> select;

		// O(1) 时间复杂度查找
		auto it = userTypeMap.find(select);
		if (it != userTypeMap.end())
		{
			cout << "you select " << it->second.label << endl;
			cout << "file path: " << it->second.fileName << endl;
			login(it);
			system("pause");
		}
		else if (select == 0)
		{
			exit(0);
		}
		else
		{
			cout << "input error, please input again!" << endl;
			system("pause");
		}
	}
	return 0;
}